const TAG = "HLK-OTA"
let isShow = false; //如果  弹过窗了则不弹
function formatLog(msg: string) {
  let date = new Date()
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()
  const mill = date.getMilliseconds()

  let timeString = `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second, mill].map(formatNumber).join(':')}`

  return timeString + ":" + TAG + "-->" + msg;
}
const formatNumber = (n: any) => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}
function setLogGrade(grade: number) {
  logGrade = grade
}
function logGroup(label: string) {
  console.group(label);
}
function logGroupEnd() {
  console.groupEnd();
}
var logGrade: number = 1;
function logv(msg: string) {
  if (logGrade <= 1) {
    console.log(formatLog(msg));
  }
}
function logd(msg: string) {
  if (logGrade <= 2) {
    console.debug(formatLog(msg));
  }
}
function logi(msg: string) {
  if (logGrade <= 3) {
    console.info(formatLog(msg));
  }

  if (getCurrentPages()[getCurrentPages().length - 1].route.match(/pageConnect/) == null) { //在非连接页时可弹,因为链接页有了
      if (msg.match(/1003/)) {
        wx.showToast({
          title:'连接失败',
          icon:'none',
          duration:2000
        })
      }
  } 
  // console.log(msg.match(/"connected":false/) && wx.getStorageSync('isShow')||msg.match(/"available":false/));
  // 连接断开
  if (msg.match(/"connected":false/) && wx.getStorageSync('isShow')|| msg.match(/"errorCode":10003/)) {
    showDialog()
  }
}
function logw(msg: string) {
  if (logGrade <= 4) {
    console.warn(formatLog(msg));
  }
  // console.log(msg.match(/"connected":false/),wx.getStorageSync('isShow')||msg.match(/"available":false/));
  // 连接断开
  if (msg.match(/"connected":false/) && wx.getStorageSync('isShow')||msg.match(/"available":false/)) {
    showDialog()
  }
}
/** 这弹窗消息啊，我有三不弹 */
export function showDialog() {
  console.log('到底',
  getCurrentPages()[getCurrentPages().length - 1].route.match(/pageConnect/) != null,
  getCurrentPages()[getCurrentPages().length - 1].route.match(/pageUpate/) != null,
  getCurrentPages()[getCurrentPages().length - 1].route.match(/pageDownload/) != null,
  wx.getStorageSync("showToast") == false
  );
  if (getCurrentPages()[getCurrentPages().length - 1].route.match(/pageConnect/) != null) {//01.在连接页时我不弹,刚连就断开,还谈个毛,修设备啦
    return;
  }
  if (getCurrentPages()[getCurrentPages().length - 1].route.match(/pageUpate/) != null) {//02.在升级页时我也不弹,都在升级了我不忍打扰
    return;
  }
  if (getCurrentPages()[getCurrentPages().length - 1].route.match(/pageDownload/) != null) { //04.在下载页时我还是不弹,因为它善
    return;
  }
  if (wx.getStorageSync("showToast") == false) { //03.如果它为false我更不能弹
    return;
  }

  
  wx.setStorageSync('isShow',true)
  const app = getApp()
  console.log('可以弹窗',);
  const isSR20:boolean = app.globalData?.device?.name.includes('sr20')
  const backUrl = isSR20?'../pages/pageConnect/pageConnect':'../pages/LB1001Connect/LB1001Connect'
  
  wx.showModal({
    title: "系统提示",
    content:'升级已完成，请重新连接',//设备已断开，请重新连接
    showCancel: false,
    confirmText: "确定",
    success:()=>{
        wx.setStorageSync('isShow',false)
        wx.navigateBack({
          // delta:backConnectPage()
          delta: 3
        })
      }
  });
}
/** 返回 页面层数 */
export function backConnectPage() {
  var index = getCurrentPages().length - 2;
  var currentPage = getCurrentPages()[getCurrentPages().length - 1].route;
  console.log('我在哪 -->',currentPage,getCurrentPages() );
  if (index<=0) {
    index = 1
  }
  console.log('返回多少层',index);
  
  return index
}
function loge(msg: string) {
   if (msg.match(/10012/)) {
    const app = getApp()
    // 
    const isSR20:boolean = app.globalData?.device?.name.includes('sr20')
    const backUrl = isSR20?'../pages/pageConnect/pageConnect':'../pages/LB1001Connect/LB1001Connect'
    // console.log('看看', backUrl );
    wx.showModal({
      title: "系统提示",
      content:'蓝牙连接失败',
      showCancel: false,
      confirmText: "确定",
      success:()=>{
        // wx.redirectTo({
        //   url:'../pages/LB1001Connect/LB1001Connect'
        // })
        // wx.redirectTo({
        //   url:backUrl
        // })
        wx.navigateBack({
          delta:backConnectPage()
        })
          // wx.navigateBack({
          //   delta:100
          // })
        }
    });
  }
  if (logGrade <= 5) {
    console.error(formatLog(msg));
  }
}

/** arraybuffer 转字符串*/
function ab2hex(buffer: ArrayBuffer) {
  const hexArr = Array.prototype.map.call(
    new Uint8Array(buffer),
    function (bit) {
      return ('00' + bit.toString(16)).slice(-2)
    }
  )
  return hexArr.join('')
}

/** 16进制数据转蓝牙地址 */
function hexDataCovetToAddress(dataArray: Uint8Array) {
  let address = ""
  for (let index = 0; index < dataArray.length; index++) {
    const element = dataArray[index];
    address += ('00' + element.toString(16)).slice(-2)
    if (index != dataArray.length - 1) {
      address += ":"
    }
  }
  return address.toUpperCase()
}
export {
  hexDataCovetToAddress,
  ab2hex,
  loge,
  logw,
  logi,
  logd,
  logv,
  logGroupEnd,
  logGroup,
  setLogGrade
}
